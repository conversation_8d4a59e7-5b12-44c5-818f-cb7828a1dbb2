# Azure 认证配置指南

本指南将帮助你配置 Azure CLI 和 Pulumi，以便部署和管理 Azure 基础设施。

## 📋 前置要求

- Azure 账户（免费账户即可开始）
- Azure CLI
- Pulumi CLI

## 🚀 快速开始

### 1. 安装 Azure CLI

#### macOS
```bash
brew install azure-cli
```



#### Linux (Ubuntu/Debian)
```bash
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
```

### 2. 登录 Azure

```bash
# 交互式登录
az login

# 如果在无头环境中，使用设备代码登录
az login --use-device-code
```

### 3. 验证登录状态

```bash
# 查看当前账户信息
az account show

# 列出所有可用订阅
az account list --output table
```

### 4. 设置默认订阅（如果有多个）

```bash
# 设置默认订阅
az account set --subscription "your-subscription-id"
```

## 🔧 Pulumi Azure 配置

### 1. 配置 Pulumi 使用 Azure

```bash
# 进入项目目录
cd /path/to/a1d-pulumi

# 创建新的 Azure stack
pulumi stack init azure-dev

# 设置 Azure 区域
pulumi config set azure-native:location "East Asia"

# 设置项目名称
pulumi config set projectName "a1d-azure"

# 设置数据库密码（加密存储）
pulumi config set --secret dbPassword "your-secure-password"
```

### 2. 验证 Azure 配置

```bash
# 检查 Azure 配置
pulumi config

# 预览部署（不会实际创建资源）
pulumi preview -f Pulumi.azure.yaml
```

## 🌍 Azure 区域选择

推荐的 Azure 区域（对应 AWS ap-southeast-2）：

- **East Asia** (香港) - 推荐，延迟最低
- **Southeast Asia** (新加坡) - 备选
- **Japan East** (东京) - 备选

```bash
# 设置区域
pulumi config set azure-native:location "East Asia"
```

## 🔐 权限配置

### 最小权限要求

你的 Azure 账户需要以下权限：

- **Contributor** - 创建和管理资源
- **User Access Administrator** - 管理资源访问权限

### 检查权限

```bash
# 查看当前用户角色分配
az role assignment list --assignee $(az account show --query user.name -o tsv) --output table
```

## 🧪 测试连接

### 1. 创建测试资源组

```bash
# 创建测试资源组
az group create --name test-rg --location "East Asia"

# 验证创建成功
az group show --name test-rg

# 清理测试资源
az group delete --name test-rg --yes --no-wait
```

### 2. 测试 Pulumi 部署

```bash
# 使用最小配置测试
pulumi up -f Pulumi.azure.yaml --target azure.resources.ResourceGroup
```

## 🔧 常见问题排查

### 问题 1：`az login` 失败

```bash
# 清除缓存重新登录
az account clear
az login
```

### 问题 2：权限被拒绝

```
ERROR: (AuthorizationFailed) The client does not have authorization to perform action
```

**解决**：联系 Azure 管理员添加 Contributor 权限

### 问题 3：订阅未找到

```
ERROR: The subscription 'xxx' could not be found
```

**解决**：
```bash
# 列出所有订阅
az account list --output table

# 设置正确的订阅
az account set --subscription "correct-subscription-id"
```

### 问题 4：区域不支持某些服务

```
ERROR: The subscription is not registered to use namespace 'Microsoft.xxx'
```

**解决**：
```bash
# 注册所需的资源提供程序
az provider register --namespace Microsoft.Storage
az provider register --namespace Microsoft.Network
az provider register --namespace Microsoft.DBforPostgreSQL
az provider register --namespace Microsoft.Cache
az provider register --namespace Microsoft.ContainerRegistry
az provider register --namespace Microsoft.Cdn
```

## 📁 配置文件位置

Azure CLI 配置保存在：

```bash
~/.azure/config          # Azure CLI 配置
~/.azure/clouds.config   # 云环境配置
~/.azure/accessTokens.json  # 访问令牌（敏感）
```

## 🎯 最佳实践

### 1. 安全实践

- 定期轮换访问密钥
- 使用 Azure Key Vault 存储敏感信息
- 启用多因素认证 (MFA)
- 使用最小权限原则

### 2. 成本控制

- 设置预算警报
- 使用 Azure Cost Management
- 选择合适的实例大小
- 定期清理未使用的资源

### 3. 监控和日志

- 启用 Azure Monitor
- 配置诊断设置
- 使用 Application Insights

## 🎉 完成

现在你已经配置好了 Azure 认证，可以：

1. ✅ 使用 `pulumi up -f Pulumi.azure.yaml` 部署 Azure 基础设施
2. ✅ 通过 `bun run scripts/azure/generate-env.ts` 生成 .env.azure
3. ✅ 使用 Azure Portal 管理资源

**下一步**：部署你的 Azure 基础设施！

```bash
# 部署 Azure 基础设施
pulumi up -f Pulumi.azure.yaml

# 生成环境变量
bun run scripts/azure/generate-env.ts
```
