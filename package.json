{"name": "a1d-pulumi", "type": "module", "scripts": {"aws:tunnel": "pnpm --filter a1d-infra-aws dev:tunnel", "aws:up": "pnpm --filter a1d-infra-aws up", "aws:preview": "pnpm --filter a1d-infra-aws preview", "aws:destroy": "pnpm --filter a1d-infra-aws destroy", "aws:config": "pnpm --filter a1d-infra-aws config", "aws:stack": "pnpm --filter a1d-infra-aws stack", "aws:test": "pnpm --filter a1d-infra-aws test:preview", "aws:env:gen": "pnpm --filter a1d-infra-aws env:gen", "azure:up": "pnpm --filter a1d-infra-azure up", "azure:preview": "pnpm --filter a1d-infra-azure preview", "azure:destroy": "pnpm --filter a1d-infra-azure destroy", "azure:config": "pnpm --filter a1d-infra-azure config", "azure:stack": "pnpm --filter a1d-infra-azure stack", "azure:test": "pnpm --filter a1d-infra-azure test:preview", "azure:env:gen": "pnpm --filter a1d-infra-azure env:gen"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.0.14", "typescript": "latest"}, "dependencies": {"@pulumi/aws": "^6.0.0", "@pulumi/awsx": "^2.0.2", "@pulumi/azure-native": "^3.5.1", "@pulumi/azuread": "^6.5.1", "@pulumi/pulumi": "^3.113.0", "arktype": "^2.1.20"}}