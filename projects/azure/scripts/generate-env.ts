#!/usr/bin/env bun

import { $ } from "bun";
import { writeFileSync } from "fs";
import { join } from "path";
import { colors } from "../utils";

type GenerateEnvOptions = {
	targetDir?: string;
	configOnly?: boolean;
	stackName?: string;
};

async function generateAzureEnv(options: GenerateEnvOptions = {}) {
	const targetDir = options.targetDir || process.cwd();
	const stackName = options.stackName || "dev";

	try {
		console.log(`${colors.blue}🔄 正在从 Azure Pulumi 栈获取配置...${colors.reset}`);

		// 获取 Pulumi 输出
		const envConfigOutput = await $`pulumi stack output envConfig -s ${stackName} --show-secrets`.text();
		const azureConfigOutput = await $`pulumi stack output azureConfig -s ${stackName}`.text();

		// 解析 JSON 输出
		const envConfig = JSON.parse(envConfigOutput.trim());
		const azureConfig = JSON.parse(azureConfigOutput.trim());

		// 获取其他重要输出
		const outputs = {
			resourceGroupName: await $`pulumi stack output resourceGroupName -s ${stackName}`.text().then(s => s.trim()),
			storageAccountName: await $`pulumi stack output storageAccountName -s ${stackName}`.text().then(s => s.trim()),
			postgresServerFqdn: await $`pulumi stack output postgresServerFqdn -s ${stackName}`.text().then(s => s.trim()),
			redisHostname: await $`pulumi stack output redisHostname -s ${stackName}`.text().then(s => s.trim()),
			acrLoginServer: await $`pulumi stack output acrLoginServer -s ${stackName}`.text().then(s => s.trim()),
			cdnUrl: await $`pulumi stack output cdnUrl -s ${stackName}`.text().then(s => s.trim()),
		};

		console.log(`${colors.green}✅ 成功获取 Azure 配置${colors.reset}`);

		// 生成 config.json (总是生成)
		const configPath = join(targetDir, "azure-config.json");
		const configData = {
			envConfig,
			azureConfig,
			outputs,
			generatedAt: new Date().toISOString(),
			platform: "azure",
		};

		writeFileSync(configPath, JSON.stringify(configData, null, 2));
		console.log(`${colors.green}✅ 已生成 azure-config.json${colors.reset}`);

		// 生成 .env.azure (如果需要)
		if (!options.configOnly) {
			// 构建 .env.azure 内容
			const envContent = `# Generated by a1d-pulumi-azure
# Azure development environment configuration
# DO NOT COMMIT THIS FILE TO VERSION CONTROL

# Platform Configuration
PLATFORM=azure
PROJECT_NAME=${envConfig.PROJECT_NAME}

# Database Configuration (Azure Database for PostgreSQL)
DATABASE_URL=${envConfig.DATABASE_URL}
POSTGRES_URL=${envConfig.DATABASE_URL}
DB_HOST=${envConfig.DB_HOST}
DB_PORT=${envConfig.DB_PORT}
DB_NAME=${envConfig.DB_NAME}
DB_USER=${envConfig.DB_USER}

# Redis Cache Configuration (Azure Cache for Redis)
REDIS_URL=${envConfig.REDIS_URL}
REDIS_HOST=${envConfig.REDIS_HOST}
REDIS_PORT=${envConfig.REDIS_PORT}

# Storage Configuration (Azure Blob Storage)
STORAGE_ACCOUNT_NAME=${envConfig.STORAGE_ACCOUNT_NAME}
STORAGE_CONTAINER_NAME=${envConfig.STORAGE_CONTAINER_NAME}
CDN_URL=${envConfig.CDN_URL}

# Container Registry (Azure Container Registry)
ACR_LOGIN_SERVER=${envConfig.ACR_LOGIN_SERVER}

# Azure Resources (for direct access if needed)
RESOURCE_GROUP_NAME=${azureConfig.RESOURCE_GROUP_NAME}
LOCATION=${azureConfig.LOCATION}
VNET_ID=${azureConfig.VNET_ID}
PUBLIC_SUBNET_ID=${azureConfig.PUBLIC_SUBNET_ID}
PRIVATE_SUBNET_ID=${azureConfig.PRIVATE_SUBNET_ID}
POSTGRES_SERVER_NAME=${azureConfig.POSTGRES_SERVER_NAME}
POSTGRES_FQDN=${azureConfig.POSTGRES_FQDN}
REDIS_NAME=${azureConfig.REDIS_NAME}
REDIS_HOSTNAME=${azureConfig.REDIS_HOSTNAME}
ACR_NAME=${azureConfig.ACR_NAME}
`;

			// 写入 .env.azure 文件
			const envPath = join(targetDir, ".env.azure");
			writeFileSync(envPath, envContent);
			console.log(`${colors.green}✅ 已生成 .env.azure${colors.reset}`);
		}

		// 显示重要信息
		console.log(`\n${colors.cyan}🔗 重要信息:${colors.reset}`);
		console.log(`📦 项目名称: ${envConfig.PROJECT_NAME}`);
		console.log(`🌐 平台: Azure`);
		console.log(`📍 区域: ${azureConfig.LOCATION}`);

		if (!options.configOnly) {
			console.log(`🗄️  数据库: ${outputs.postgresServerFqdn}:5432`);
			console.log(`🚀 Redis: ${outputs.redisHostname}:6380 (SSL)`);
		}

		console.log(`🌐 CDN: ${outputs.cdnUrl}`);
		console.log(`📦 存储: ${outputs.storageAccountName}`);
		console.log(`🐳 容器注册表: ${outputs.acrLoginServer}`);

		if (!options.configOnly) {
			console.log(`\n${colors.yellow}💡 提示:${colors.reset}`);
			console.log("1. Azure 数据库和 Redis 部署在私有网络中");
			console.log("2. 本地开发需要配置 VPN 或使用 Azure Bastion");
			console.log("3. 查看 Azure 部署指南: docs/azure/azure-deployment-guide.md");
		}

	} catch (error) {
		console.error(`${colors.red}❌ 生成配置失败:${colors.reset}`, error);
		console.error(`\n${colors.yellow}请确保:${colors.reset}`);
		console.error("1. 已经运行 'pulumi up' 部署 Azure 基础设施");
		console.error("2. 当前在正确的 Pulumi stack 中");
		console.error("3. Azure CLI 已配置并登录");
		process.exit(1);
	}
}

// 解析命令行参数
const args = process.argv.slice(2);
const options: GenerateEnvOptions = {};

// 解析参数
for (let i = 0; i < args.length; i++) {
	const arg = args[i];
	
	if (arg === "--config-only") {
		options.configOnly = true;
	} else if (arg === "--stack" && i + 1 < args.length) {
		options.stackName = args[i + 1];
		i++; // 跳过下一个参数
	} else if (!arg.startsWith("--")) {
		// 第一个非选项参数作为目标目录
		options.targetDir = arg;
	}
}

// 显示使用说明
if (args.includes("--help") || args.includes("-h")) {
	console.log(`
${colors.cyan}Azure 环境变量生成器${colors.reset}

用法:
  bun run scripts/azure/generate-env.ts [目标目录] [选项]

选项:
  --config-only    只生成配置文件，不生成 .env.azure
  --stack <name>   指定 Pulumi stack 名称 (默认: dev)
  --help, -h       显示此帮助信息

示例:
  bun run scripts/azure/generate-env.ts
  bun run scripts/azure/generate-env.ts /path/to/project
  bun run scripts/azure/generate-env.ts --config-only
  bun run scripts/azure/generate-env.ts --stack production
	`);
	process.exit(0);
}

// 执行生成
generateAzureEnv(options);
