{"name": "a1d-infra-azure", "type": "module", "description": "Azure Infrastructure for a1d project", "scripts": {"up": "pulumi up", "preview": "pulumi preview", "destroy": "pulumi destroy", "refresh": "pulumi refresh", "config": "pulumi config", "stack": "pulumi stack", "test:up": "cp Pulumi.yaml Pulumi.yaml.backup && cp Pulumi.minimal.yaml Pulumi.yaml && pulumi up && mv Pulumi.yaml.backup Pulumi.yaml", "test:preview": "cp Pulumi.yaml Pulumi.yaml.backup && cp Pulumi.minimal.yaml Pulumi.yaml && pulumi preview && mv Pulumi.yaml.backup Pulumi.yaml", "test:destroy": "cp Pulumi.yaml Pulumi.yaml.backup && cp Pulumi.minimal.yaml Pulumi.yaml && pulumi destroy && mv Pulumi.yaml.backup Pulumi.yaml", "env:gen": "bun run scripts/generate-env.ts"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.0.14", "typescript": "latest"}, "dependencies": {"@pulumi/azure-native": "^3.5.1", "@pulumi/azuread": "^6.5.1", "@pulumi/pulumi": "^3.113.0", "arktype": "^2.1.20"}}