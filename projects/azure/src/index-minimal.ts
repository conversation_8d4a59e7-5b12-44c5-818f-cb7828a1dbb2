import * as pulumi from "@pulumi/pulumi";
import * as azure from "@pulumi/azure-native";

// Get configuration from Pulumi config
const config = new pulumi.Config();
const projectName = config.get("projectName") || "a1d-azure";
const location = config.get("location") || "East Asia";

// Create Resource Group - Azure 中所有资源的容器
const resourceGroup = new azure.resources.ResourceGroup(`${projectName}-test-rg`, {
	location: location,
	tags: {
		Project: projectName,
		Environment: "test",
		Platform: "azure",
	},
});

// Create Storage Account for testing
// Azure storage account names: 3-24 chars, lowercase letters and numbers only
const sanitizedProjectName = projectName.toLowerCase().replace(/[^a-z0-9]/g, '');
const testStorageAccountName = sanitizedProjectName.substring(0, 13) + 'teststorage'; // Max 24 chars
const storageAccount = new azure.storage.StorageAccount(testStorageAccountName, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	sku: {
		name: azure.storage.SkuName.Standard_LRS,
	},
	kind: azure.storage.Kind.StorageV2,
	accessTier: azure.storage.AccessTier.Hot,
	tags: {
		Project: projectName,
		Environment: "test",
		Platform: "azure",
	},
});

// Export important values
export const resourceGroupName = resourceGroup.name;
export const resourceGroupLocation = resourceGroup.location;
export const minimalStorageAccountName = storageAccount.name;
export const storageAccountPrimaryEndpoint = storageAccount.primaryEndpoints.apply(e => e.blob);

// Simple test message
export const message = "Azure connection successful! Storage Account created.";
