import * as pulumi from "@pulumi/pulumi";
import * as azure from "@pulumi/azure-native";
import { getClientConfig } from "@pulumi/azure-native/authorization";

// Get configuration from Pulumi config
const config = new pulumi.Config();
const dbPassword = config.requireSecret("dbPassword");
const projectName = config.get("projectName") || "a1d-azure";
const location = config.get("location") || "East Asia"; // 对应 AWS ap-southeast-2

// Define common tags for all resources
const commonTags = {
	Project: projectName,
	Environment: "development",
	CostCenter: "engineering",
	Platform: "azure",
};

// Create Resource Group - Azure 中所有资源的容器
const resourceGroup = new azure.resources.ResourceGroup(`${projectName}-rg`, {
	location: location,
	tags: commonTags,
});

// Create Storage Account for blob storage (equivalent to S3)
// Azure storage account names: 3-24 chars, lowercase letters and numbers only
const sanitizedProjectName = projectName.toLowerCase().replace(/[^a-z0-9]/g, '');
const storageAccountName = sanitizedProjectName.substring(0, 16) + 'storage'; // Max 24 chars
const storageAccount = new azure.storage.StorageAccount(storageAccountName, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	sku: {
		name: azure.storage.SkuName.Standard_LRS,
	},
	kind: azure.storage.Kind.StorageV2,
	accessTier: azure.storage.AccessTier.Hot,
	allowBlobPublicAccess: true,
	tags: commonTags,
});

// Create a container for static files (equivalent to S3 bucket)
const staticContainer = new azure.storage.BlobContainer(`${projectName}-static`, {
	resourceGroupName: resourceGroup.name,
	accountName: storageAccount.name,
	containerName: "static",
	publicAccess: azure.storage.PublicAccess.Blob,
});

// Create Virtual Network (equivalent to AWS VPC)
const vnet = new azure.network.VirtualNetwork(`${projectName}-vnet`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	addressSpace: {
		addressPrefixes: ["10.0.0.0/16"],
	},
	tags: commonTags,
});

// Create public subnet (equivalent to AWS public subnet)
const publicSubnet = new azure.network.Subnet(`${projectName}-public-subnet`, {
	resourceGroupName: resourceGroup.name,
	virtualNetworkName: vnet.name,
	addressPrefix: "********/24",
	subnetName: "public",
});

// Create private subnet (equivalent to AWS private subnet)
const privateSubnet = new azure.network.Subnet(`${projectName}-private-subnet`, {
	resourceGroupName: resourceGroup.name,
	virtualNetworkName: vnet.name,
	addressPrefix: "********/24",
	subnetName: "private",
});

// Create Network Security Group for database (equivalent to AWS security group)
const dbNsg = new azure.network.NetworkSecurityGroup(`${projectName}-db-nsg`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	securityRules: [
		{
			name: "AllowPostgreSQL",
			protocol: azure.network.SecurityRuleProtocol.Tcp,
			sourcePortRange: "*",
			destinationPortRange: "5432",
			sourceAddressPrefix: "10.0.0.0/16",
			destinationAddressPrefix: "*",
			access: azure.network.SecurityRuleAccess.Allow,
			priority: 1000,
			direction: azure.network.SecurityRuleDirection.Inbound,
		},
	],
	tags: commonTags,
});

// Create Network Security Group for Redis
const redisNsg = new azure.network.NetworkSecurityGroup(`${projectName}-redis-nsg`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	securityRules: [
		{
			name: "AllowRedis",
			protocol: azure.network.SecurityRuleProtocol.Tcp,
			sourcePortRange: "*",
			destinationPortRange: "6379",
			sourceAddressPrefix: "10.0.0.0/16",
			destinationAddressPrefix: "*",
			access: azure.network.SecurityRuleAccess.Allow,
			priority: 1000,
			direction: azure.network.SecurityRuleDirection.Inbound,
		},
	],
	tags: commonTags,
});

// Create Network Security Group for Container Apps
const containerAppsNsg = new azure.network.NetworkSecurityGroup(`${projectName}-containerapp-nsg`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	securityRules: [
		{
			name: "AllowHTTP",
			protocol: azure.network.SecurityRuleProtocol.Tcp,
			sourcePortRange: "*",
			destinationPortRange: "80",
			sourceAddressPrefix: "*",
			destinationAddressPrefix: "*",
			access: azure.network.SecurityRuleAccess.Allow,
			priority: 1000,
			direction: azure.network.SecurityRuleDirection.Inbound,
		},
		{
			name: "AllowHTTPS",
			protocol: azure.network.SecurityRuleProtocol.Tcp,
			sourcePortRange: "*",
			destinationPortRange: "443",
			sourceAddressPrefix: "*",
			destinationAddressPrefix: "*",
			access: azure.network.SecurityRuleAccess.Allow,
			priority: 1001,
			direction: azure.network.SecurityRuleDirection.Inbound,
		},
		{
			name: "AllowCustomPorts",
			protocol: azure.network.SecurityRuleProtocol.Tcp,
			sourcePortRange: "*",
			destinationPortRange: "3000-8080",
			sourceAddressPrefix: "*",
			destinationAddressPrefix: "*",
			access: azure.network.SecurityRuleAccess.Allow,
			priority: 1002,
			direction: azure.network.SecurityRuleDirection.Inbound,
		},
		{
			name: "AllowOutbound",
			protocol: azure.network.SecurityRuleProtocol.Tcp,
			sourcePortRange: "*",
			destinationPortRange: "*",
			sourceAddressPrefix: "*",
			destinationAddressPrefix: "*",
			access: azure.network.SecurityRuleAccess.Allow,
			priority: 1000,
			direction: azure.network.SecurityRuleDirection.Outbound,
		},
	],
	tags: commonTags,
});

// Create Log Analytics Workspace for Container Apps logging
const logAnalyticsWorkspace = new azure.operationalinsights.Workspace(`${projectName}-logs`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	workspaceName: `${projectName}-logs`,
	sku: {
		name: azure.operationalinsights.WorkspaceSkuNameEnum.PerGB2018,
	},
	retentionInDays: 30,
	tags: commonTags,
});

// Create Container Apps Environment (equivalent to AWS ECS Cluster)
const containerAppsEnvironment = new azure.app.ManagedEnvironment(`${projectName}-containerapp-env`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	environmentName: `${projectName}-containerapp-env`,
	vnetConfiguration: {
		infrastructureSubnetId: publicSubnet.id,
	},
	appLogsConfiguration: {
		destination: "log-analytics",
		logAnalyticsConfiguration: {
			customerId: logAnalyticsWorkspace.customerId,
					sharedKey: azure.operationalinsights.getSharedKeysOutput({
			resourceGroupName: resourceGroup.name,
			workspaceName: logAnalyticsWorkspace.name,
		}).primarySharedKey?.apply(key => key || "") || "",
		},
	},
	tags: commonTags,
});

// Create Azure Database for PostgreSQL Flexible Server (equivalent to AWS RDS)
const postgresServer = new azure.dbforpostgresql.Server(`${projectName}-postgres`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	serverName: `${projectName}-postgres`,
	sku: {
		name: "Standard_B1ms", // ARM-based instance for cost optimization
		tier: azure.dbforpostgresql.SkuTier.Burstable,
	},
	storage: {
		storageSizeGB: 32,
	},
	version: azure.dbforpostgresql.ServerVersion.ServerVersion_16,
	administratorLogin: "postgres",
	administratorLoginPassword: dbPassword,
	network: {
		delegatedSubnetResourceId: privateSubnet.id,
		privateDnsZoneArmResourceId: pulumi.interpolate`/subscriptions/${getClientConfig().then((c: any) => c.subscriptionId)}/resourceGroups/${resourceGroup.name}/providers/Microsoft.Network/privateDnsZones/postgres.database.azure.com`,
	},
	tags: commonTags,
});

// Create database
const database = new azure.dbforpostgresql.Database(`${projectName}-maindb`, {
	resourceGroupName: resourceGroup.name,
	serverName: postgresServer.name,
	databaseName: "maindb",
	charset: "UTF8",
	collation: "en_US.utf8",
});

// Create Azure Cache for Redis (using correct v3+ API)
const redisCache = new azure.redis.Redis(`${projectName}-redis`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	sku: {
		name: azure.redis.SkuName.Premium,
		family: azure.redis.SkuFamily.P,
		capacity: 1, // P1 = Premium 1 GB
	},
	enableNonSslPort: false,
	minimumTlsVersion: azure.redis.TlsVersion.TlsVersion_1_2,
	redisConfiguration: {
		maxmemoryPolicy: "allkeys-lru"
	},
	subnetId: privateSubnet.id, // Deploy in private subnet
	tags: commonTags,
});

// Get Redis primary key
const redisPrimaryKey = azure.redis.listRedisKeysOutput({
	name: redisCache.name,
	resourceGroupName: resourceGroup.name,
}).primaryKey;

// Create Azure Container Registry (equivalent to AWS ECR)
// Azure ACR names: 5-50 chars, alphanumeric only
const acrRegistryName = sanitizedProjectName.substring(0, 47) + 'acr'; // Max 50 chars
const containerRegistry = new azure.containerregistry.Registry(acrRegistryName, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	registryName: acrRegistryName,
	sku: {
		name: azure.containerregistry.SkuName.Basic,
	},
	adminUserEnabled: true,
	tags: commonTags,
});

// Create CDN Profile and Endpoint (equivalent to AWS CloudFront)
const cdnProfile = new azure.cdn.Profile(`${projectName}-cdn-profile`, {
	resourceGroupName: resourceGroup.name,
	location: "Global", // CDN profiles are global
	profileName: `${projectName}-cdn-profile`,
	sku: {
		name: azure.cdn.SkuName.Standard_Microsoft,
	},
	tags: commonTags,
});

const cdnEndpoint = new azure.cdn.Endpoint(`${projectName}-cdn-endpoint`, {
	resourceGroupName: resourceGroup.name,
	profileName: cdnProfile.name,
	endpointName: `${projectName}-cdn`,
	location: "Global",
	origins: [{
		name: "storage-origin",
		hostName: storageAccount.primaryEndpoints.apply(endpoints =>
			endpoints.blob?.replace("https://", "").replace("/", "") || ""
		),
		httpPort: 80,
		httpsPort: 443,
	}],
	isHttpAllowed: false,
	isHttpsAllowed: true,
	originHostHeader: storageAccount.primaryEndpoints.apply(endpoints =>
		endpoints.blob?.replace("https://", "").replace("/", "") || ""
	),
	tags: commonTags,
});

// Create Key Vault for secrets management
const keyVault = new azure.keyvault.Vault(`${projectName}-kv`, {
	resourceGroupName: resourceGroup.name,
	location: resourceGroup.location,
	properties: {
		sku: {
			family: azure.keyvault.SkuFamily.A,
			name: azure.keyvault.SkuName.Standard,
		},
		tenantId: getClientConfig().then(config => config.tenantId),
		accessPolicies: [], // Will use RBAC instead
		enableRbacAuthorization: true,
		enableSoftDelete: true,
		softDeleteRetentionInDays: 7,
		enablePurgeProtection: true,
	},
	tags: commonTags,
});

// Store database password in Key Vault
const dbPasswordSecret = new azure.keyvault.Secret(`${projectName}-db-password`, {
	resourceGroupName: resourceGroup.name,
	vaultName: keyVault.name,
	secretName: "database-password",
	properties: {
		value: dbPassword,
	},
});

// Store Redis key in Key Vault
const redisKeySecret = new azure.keyvault.Secret(`${projectName}-redis-key`, {
	resourceGroupName: resourceGroup.name,
	vaultName: keyVault.name,
	secretName: "redis-primary-key",
	properties: {
		value: redisPrimaryKey,
	},
});

// Export important values for use by other projects
export const resourceGroupName = resourceGroup.name;
export const resourceGroupLocation = resourceGroup.location;

// Export Storage Account information
export const azureStorageAccountName = storageAccount.name;
export const storageAccountPrimaryEndpoint = storageAccount.primaryEndpoints.apply(e => e.blob);
export const staticContainerName = staticContainer.name;

// Export CDN information
export const cdnEndpointHostname = cdnEndpoint.hostName;
export const cdnUrl = pulumi.interpolate`https://${cdnEndpoint.hostName}`;

// Export Virtual Network information
export const vnetId = vnet.id;
export const vnetName = vnet.name;
export const publicSubnetId = publicSubnet.id;
export const privateSubnetId = privateSubnet.id;
export const vnetCidrBlock = "10.0.0.0/16";

// Export Database information
export const postgresServerName = postgresServer.name;
export const postgresServerFqdn = postgresServer.fullyQualifiedDomainName;
export const postgresPort = 5432;
export const databaseName = database.name;

// Export Redis information
export const redisName = redisCache.name;
export const redisHostname = redisCache.hostName;
export const redisPort = redisCache.port;
export const redisSslPort = redisCache.sslPort;
export const redisPrimaryKeyOutput = redisPrimaryKey;

// Export Container Registry information
export const acrName = containerRegistry.name;
export const acrLoginServer = containerRegistry.loginServer;

// Export Container Apps Environment information
export const containerAppsEnvironmentId = containerAppsEnvironment.id;
export const containerAppsEnvironmentName = containerAppsEnvironment.name;
export const logAnalyticsWorkspaceId = logAnalyticsWorkspace.id;
export const containerAppsNsgId = containerAppsNsg.id;

// Export Key Vault information
export const keyVaultName = keyVault.name;
export const keyVaultUri = keyVault.properties.apply(props => props?.vaultUri);
export const dbPasswordSecretName = dbPasswordSecret.name;
export const redisKeySecretName = redisKeySecret.name;

// Export environment configuration for applications
export const envConfig = pulumi.jsonStringify({
	PROJECT_NAME: projectName,
	PLATFORM: "azure",

	// Database Configuration
	DATABASE_URL: pulumi.interpolate`postgresql://postgres:${dbPassword}@${postgresServer.fullyQualifiedDomainName}:5432/maindb?sslmode=require`,
	DB_HOST: postgresServer.fullyQualifiedDomainName,
	DB_PORT: "5432",
	DB_NAME: "maindb",
	DB_USER: "postgres",

	// Redis Configuration
	// Redis Configuration (secure connection)
	REDIS_URL: pulumi.interpolate`rediss://:${redisPrimaryKey}@${redisCache.hostName}:${redisCache.sslPort}`,
	REDIS_HOST: redisCache.hostName,
	REDIS_PORT: redisCache.sslPort,
	REDIS_KEY: redisPrimaryKey,

	// Storage Configuration
	STORAGE_ACCOUNT_NAME: storageAccount.name,
	STORAGE_CONTAINER_NAME: staticContainer.name,
	CDN_URL: pulumi.interpolate`https://${cdnEndpoint.hostName}`,

	// Container Registry
	ACR_LOGIN_SERVER: containerRegistry.loginServer,
});

// Export Azure-specific configuration for container apps
export const azureConfig = pulumi.jsonStringify({
	RESOURCE_GROUP_NAME: resourceGroup.name,
	LOCATION: resourceGroup.location,
	VNET_ID: vnet.id,
	PUBLIC_SUBNET_ID: publicSubnet.id,
	PRIVATE_SUBNET_ID: privateSubnet.id,
	DB_NSG_ID: dbNsg.id,
	REDIS_NSG_ID: redisNsg.id,
	CONTAINER_APPS_NSG_ID: containerAppsNsg.id,
	POSTGRES_SERVER_NAME: postgresServer.name,
	POSTGRES_FQDN: postgresServer.fullyQualifiedDomainName,
	REDIS_NAME: redisCache.name,
	REDIS_HOSTNAME: redisCache.hostName,
	ACR_NAME: containerRegistry.name,
	ACR_LOGIN_SERVER: containerRegistry.loginServer,
	STORAGE_ACCOUNT_NAME: storageAccount.name,
	CDN_PROFILE_NAME: cdnProfile.name,
	CDN_ENDPOINT_NAME: cdnEndpoint.name,
	CONTAINER_APPS_ENVIRONMENT_ID: containerAppsEnvironment.id,
	CONTAINER_APPS_ENVIRONMENT_NAME: containerAppsEnvironment.name,
	LOG_ANALYTICS_WORKSPACE_ID: logAnalyticsWorkspace.id,
	KEY_VAULT_NAME: keyVault.name,
	KEY_VAULT_URI: keyVault.properties.apply(props => props?.vaultUri || ""),
});

// Get storage account keys for connection string
const storageKeys = azure.storage.listStorageAccountKeysOutput({
	resourceGroupName: resourceGroup.name,
	accountName: storageAccount.name,
});

const storageConnectionString = pulumi.interpolate`DefaultEndpointsProtocol=https;AccountName=${storageAccount.name};AccountKey=${storageKeys.keys[0].value};EndpointSuffix=core.windows.net`;

// Generate .env.azure file with all configuration
export const envFileContent = pulumi.all([
	redisCache.hostName,
	redisPrimaryKey,
	storageConnectionString,
	postgresServer.fullyQualifiedDomainName,
	containerRegistry.loginServer,
	keyVault.properties.apply(props => props?.vaultUri || ""),
]).apply(([redisHost, redisKey, storageConn, pgHost, acrServer, kvUri]) => {
	const content = [
		"# Azure Infrastructure Environment Variables",
		"# Generated automatically by Pulumi",
		"",
		"# Redis Configuration",
		`REDIS_URL=rediss://:${redisKey}@${redisHost}:6380`,
		`REDIS_HOST=${redisHost}`,
		`REDIS_PORT=6380`,
		`REDIS_KEY=${redisKey}`,
		"",
		"# PostgreSQL Configuration",
		`POSTGRES_HOST=${pgHost}`,
		`POSTGRES_PORT=5432`,
		`POSTGRES_USER=postgres`,
		`POSTGRES_DB=maindb`,
		"# Note: Use Key Vault reference for password: @Microsoft.KeyVault(SecretUri=${kvUri}secrets/database-password/)",
		"",
		"# Storage Configuration",
		`AZURE_STORAGE_CONNECTION_STRING=${storageConn}`,
		`AZURE_STORAGE_ACCOUNT_NAME=${sanitizedProjectName}storage`,
		`AZURE_STORAGE_CONTAINER_NAME=static`,
		"",
		"# Container Registry",
		`ACR_LOGIN_SERVER=${acrServer}`,
		"",
		"# Key Vault",
		`KEY_VAULT_URI=${kvUri}`,
		"",
		"# Azure Resource Information",
		`AZURE_RESOURCE_GROUP=${sanitizedProjectName}-rg`,
		`AZURE_LOCATION=East Asia`,
		"",
	].join("\n");

	// Optionally write to file (uncomment in development)
	// require("fs").writeFileSync(".env.azure", content);

	return content;
});
