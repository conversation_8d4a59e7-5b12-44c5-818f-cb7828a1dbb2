#!/usr/bin/env bun

import { execSync } from "child_process";
import { writeFileSync, existsSync, mkdirSync } from "fs";
import { join, resolve } from "path";

type GenerateOptions = {
	envOnly?: boolean;
	configOnly?: boolean;
	targetDir?: string;
}

async function generateConfig(options: GenerateOptions = {}) {
	try {
		// 获取目标目录（默认为当前目录）
		const targetDir = options.targetDir ? resolve(options.targetDir) : process.cwd();

		// 确保目标目录存在
		if (!existsSync(targetDir)) {
			mkdirSync(targetDir, { recursive: true });
		}

		console.log(`📦 正在生成配置文件: ${targetDir}`);

		// 获取所有 stack 输出（包括密码）
		const stackOutputs = execSync("pulumi stack output --json --show-secrets", {
			encoding: "utf8",
			cwd: process.cwd()
		});

		const outputs = JSON.parse(stackOutputs);

		// 解析嵌套的 JSON 字符串
		const envConfig = outputs.envConfig ? JSON.parse(outputs.envConfig) : {};
		const fargateConfig = outputs.fargateConfig ? JSON.parse(outputs.fargateConfig) : {};

		// 生成 pulumi-config.json (如果需要)
		if (!options.envOnly) {
			const config = {
				...envConfig,
				...fargateConfig,
				// 添加其他直接输出
				BUCKET_ARN: outputs.bucketArn,
				BUCKET_DOMAIN_NAME: outputs.bucketDomainName,
				CLOUDFRONT_DISTRIBUTION_ID: outputs.cloudFrontDistributionId,
				CLOUDFRONT_DOMAIN_NAME: outputs.cloudFrontDomainName,
				CLOUDFRONT_URL: outputs.cloudFrontUrl,
				ORIGIN_ACCESS_IDENTITY_ID: outputs.originAccessIdentityId,
				// 添加数据库密码（开发环境）
				DB_PASSWORD: outputs.postgresPassword,
			};

			writeFileSync(join(targetDir, "pulumi-config.json"), JSON.stringify(config, null, 2));
			console.log("✅ 已生成 pulumi-config.json");
		}

		// 生成 .env.local (如果需要)
		if (!options.configOnly) {
			// 构建 .env.local 内容
			const envContent = `# Generated by a1d-pulumi
# Development environment configuration
# DO NOT COMMIT THIS FILE TO VERSION CONTROL

# Database Configuration (通过隧道访问)
DATABASE_URL=${envConfig.DATABASE_URL}
POSTGRES_URL=${envConfig.DATABASE_URL}
DB_HOST=localhost
DB_PORT=5432
DB_NAME=maindb
DB_USER=postgres
DB_PASSWORD=${outputs.postgresPassword}

# Redis Cache Configuration (通过隧道访问)
REDIS_URL=${envConfig.REDIS_URL?.replace(outputs.redisEndpoint, 'localhost')}
REDIS_HOST=localhost
REDIS_PORT=6379

# CDN Configuration
CDN_URL=${envConfig.CDN_URL}
CDN_DISTRIBUTION_ID=${outputs.cloudFrontDistributionId}
S3_BUCKET_NAME=${outputs.bucketName}

# Project Configuration
PROJECT_NAME=${envConfig.PROJECT_NAME}

# AWS Resources (for direct access if needed)
POSTGRES_ENDPOINT=${outputs.postgresEndpoint}
REDIS_ENDPOINT=${outputs.redisEndpoint}
VPC_ID=${outputs.vpcId}
ECR_REPOSITORY_URL=${outputs.ecrRepositoryUrl}
BASTION_INSTANCE_ID=${outputs.bastionInstanceId}
`;

			// 写入 .env.local 文件
			const envPath = join(targetDir, ".env.local");
			writeFileSync(envPath, envContent);
			console.log(`✅ 已生成 .env.local`);
		}

		// 显示重要信息
		console.log("\n🔗 重要信息:");
		console.log(`📦 项目名称: ${envConfig.PROJECT_NAME}`);

		if (!options.configOnly) {
			console.log(`🗄️  数据库: localhost:5432 (需要启动隧道)`);
			console.log(`🚀 Redis: localhost:6379 (需要启动隧道)`);
		}

		console.log(`🌐 CDN: ${envConfig.CDN_URL}`);
		console.log(`📦 S3: ${outputs.bucketName}`);

		if (!options.configOnly) {
			console.log("\n💡 提示:");
			console.log("1. 使用前请先启动本地隧道: bun run scripts/dev-tunnel.ts");
			console.log("2. 查看隧道使用指南: docs/local-development-tunnels.md");
		}

	} catch (error) {
		console.error("❌ 生成配置失败:", error);
		console.error("\n请确保:");
		console.error("1. 已经运行 'pulumi up' 部署基础设施");
		console.error("2. 当前在正确的 Pulumi stack 中");
		process.exit(1);
	}
}

function main() {
	const args = process.argv.slice(2);
	const options: GenerateOptions = {};

	// 解析命令行参数
	for (let i = 0; i < args.length; i++) {
		const arg = args[i];
		switch (arg) {
			case '--env-only':
				options.envOnly = true;
				break;
			case '--config-only':
				options.configOnly = true;
				break;
			case '--target':
				options.targetDir = args[++i];
				break;
			default:
				// 向后兼容：第一个参数作为目标目录
				if (!options.targetDir && !arg.startsWith('--')) {
					options.targetDir = arg;
				}
				break;
		}
	}

	generateConfig(options);
}

if (import.meta.main) {
	main();
}