#!/usr/bin/env bun

import { $ } from "bun"
import { colors } from "./utils"

const STACK_NAME = "ethan-huo-org/a1d-pulumi/dev";

async function checkCommand(command: string): Promise<boolean> {
	try {
		await $`which ${command}`.quiet();
		return true;
	} catch {
		return false;
	}
}

async function getPulumiOutput(key: string): Promise<string> {
	try {
		const result = await $`pulumi stack output ${key} -s ${STACK_NAME}`.text();
		return result.trim();
	} catch {
		return "";
	}
}

async function startTunnel(
	bastionId: string,
	remotePort: number,
	localPort: number
) {
	const parameters = JSON.stringify({
		portNumber: [remotePort.toString()],
		localPortNumber: [localPort.toString()],
	});

	return $`aws ssm start-session --target ${bastionId} --document-name AWS-StartPortForwardingSession --parameters ${parameters}`;
}

async function main() {
	console.log(colors.blue("🚇 启动开发隧道..."));

	// 检查必需的工具
	const requiredTools = [
		{
			name: "session-manager-plugin",
			installCmd: "brew install --cask session-manager-plugin",
		},
		{ name: "aws", installCmd: "brew install awscli" },
		{ name: "pulumi", installCmd: "brew install pulumi" },
	];

	for (const tool of requiredTools) {
		if (!(await checkCommand(tool.name))) {
			console.log(colors.red(`❌ ${tool.name} 未安装`));
			console.log(colors.yellow(`请运行: ${tool.installCmd}`));
			process.exit(1);
		}
	}

	console.log(colors.yellow("📋 获取基础设施信息..."));

	// 获取 Bastion 信息
	const bastionId = await getPulumiOutput("bastionInstanceId");
	if (!bastionId) {
		console.log(colors.red("❌ 无法获取 Bastion 实例 ID"));
		console.log(colors.yellow("请确保已部署基础设施：pulumi up"));
		process.exit(1);
	}

	const dbEndpoint = await getPulumiOutput("postgresEndpoint");
	const redisEndpoint = await getPulumiOutput("redisEndpoint");

	console.log(colors.green(`✓ Bastion ID: ${bastionId}`));
	console.log(colors.green(`✓ DB Endpoint: ${dbEndpoint}`));
	console.log(colors.green(`✓ Redis Endpoint: ${redisEndpoint}`));

	// 启动隧道
	console.log(colors.yellow("🔗 启动 PostgreSQL 隧道 (localhost:5432)..."));
	const pgTunnel = startTunnel(bastionId, 5432, 5432);

	console.log(colors.yellow("🔗 启动 Redis 隧道 (localhost:6379)..."));
	const redisTunnel = startTunnel(bastionId, 6379, 6379);

	console.log(colors.green("✅ 隧道已启动！"));
	console.log(colors.blue("📝 连接信息："));
	console.log(
		"   PostgreSQL: postgresql://postgres:YOUR_PASSWORD@localhost:5432/maindb"
	);
	console.log("   Redis: redis://localhost:6379");
	console.log(colors.yellow("💡 按 Ctrl+C 停止隧道"));

	// 等待进程或用户中断
	const cleanup = () => {
		console.log(colors.yellow("\n🛑 正在停止隧道..."));
		process.exit(0);
	};

	process.on("SIGINT", cleanup);
	process.on("SIGTERM", cleanup);

	// 等待隧道进程
	await Promise.all([pgTunnel, redisTunnel]);
}

if (import.meta.main) {
	main().catch((error) => {
		console.error(colors.red("❌ 错误:"), error.message);
		process.exit(1);
	});
}
