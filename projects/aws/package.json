{"name": "a1d-infra-aws", "type": "module", "description": "AWS Infrastructure for a1d project", "scripts": {"up": "pulumi up", "preview": "pulumi preview", "destroy": "pulumi destroy", "refresh": "pulumi refresh", "config": "pulumi config", "stack": "pulumi stack", "test:up": "pulumi up -f <PERSON>ulumi.minimal.yaml", "test:preview": "pulumi preview -f Pulumi.minimal.yaml", "test:destroy": "pulumi destroy -f <PERSON><PERSON>i.minimal.yaml", "env:gen": "bun run scripts/generate-env.ts", "dev:tunnel": "bun run scripts/dev-tunnel.ts"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.0.14", "typescript": "latest"}, "dependencies": {"@pulumi/aws": "^6.0.0", "@pulumi/awsx": "^2.0.2", "@pulumi/pulumi": "^3.113.0", "arktype": "^2.1.20"}}